export interface ApiError {
  message: string;
  status?: number;
  code?: string;
  details?: any;
}

export class AppError extends Error {
  public readonly status: number;
  public readonly code: string;
  public readonly isOperational: boolean;

  constructor(message: string, status: number = 500, code: string = 'INTERNAL_ERROR', isOperational: boolean = true) {
    super(message);
    this.status = status;
    this.code = code;
    this.isOperational = isOperational;
    
    // Maintains proper stack trace for where our error was thrown (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, AppError);
    }
  }
}

export const ERROR_CODES = {
  // Network errors
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  
  // Authentication errors
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  
  // Resource errors
  NOT_FOUND: 'NOT_FOUND',
  CONFLICT: 'CONFLICT',
  
  // Validation errors
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  INVALID_INPUT: 'INVALID_INPUT',
  
  // Server errors
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
  
  // Rate limiting
  RATE_LIMITED: 'RATE_LIMITED',
} as const;

export const ERROR_MESSAGES = {
  [ERROR_CODES.NETWORK_ERROR]: 'Unable to connect to the server. Please check your internet connection.',
  [ERROR_CODES.TIMEOUT_ERROR]: 'The request timed out. Please try again.',
  [ERROR_CODES.UNAUTHORIZED]: 'You need to sign in to access this resource.',
  [ERROR_CODES.FORBIDDEN]: 'You don\'t have permission to access this resource.',
  [ERROR_CODES.TOKEN_EXPIRED]: 'Your session has expired. Please sign in again.',
  [ERROR_CODES.NOT_FOUND]: 'The requested resource was not found.',
  [ERROR_CODES.CONFLICT]: 'There was a conflict with your request.',
  [ERROR_CODES.VALIDATION_ERROR]: 'Please check your input and try again.',
  [ERROR_CODES.INVALID_INPUT]: 'The provided information is invalid.',
  [ERROR_CODES.INTERNAL_ERROR]: 'An unexpected error occurred. Please try again later.',
  [ERROR_CODES.SERVICE_UNAVAILABLE]: 'The service is temporarily unavailable. Please try again later.',
  [ERROR_CODES.RATE_LIMITED]: 'Too many requests. Please wait a moment before trying again.',
} as const;

/**
 * Parses an error from an API response or other source
 */
export function parseError(error: any): ApiError {
  // If it's already an AppError, return its properties
  if (error instanceof AppError) {
    return {
      message: error.message,
      status: error.status,
      code: error.code,
    };
  }

  // Handle fetch/network errors
  if (error instanceof TypeError && error.message.includes('fetch')) {
    return {
      message: ERROR_MESSAGES[ERROR_CODES.NETWORK_ERROR],
      status: 0,
      code: ERROR_CODES.NETWORK_ERROR,
    };
  }

  // Handle timeout errors
  if (error.name === 'AbortError' || error.message.includes('timeout')) {
    return {
      message: ERROR_MESSAGES[ERROR_CODES.TIMEOUT_ERROR],
      status: 408,
      code: ERROR_CODES.TIMEOUT_ERROR,
    };
  }

  // Handle HTTP errors with status codes
  if (error.status || error.response?.status) {
    const status = error.status || error.response.status;
    let code: string;
    let message: string;

    switch (status) {
      case 400:
        code = ERROR_CODES.VALIDATION_ERROR;
        message = error.message || ERROR_MESSAGES[code];
        break;
      case 401:
        code = ERROR_CODES.UNAUTHORIZED;
        message = ERROR_MESSAGES[code];
        break;
      case 403:
        code = ERROR_CODES.FORBIDDEN;
        message = ERROR_MESSAGES[code];
        break;
      case 404:
        code = ERROR_CODES.NOT_FOUND;
        message = ERROR_MESSAGES[code];
        break;
      case 409:
        code = ERROR_CODES.CONFLICT;
        message = error.message || ERROR_MESSAGES[code];
        break;
      case 429:
        code = ERROR_CODES.RATE_LIMITED;
        message = ERROR_MESSAGES[code];
        break;
      case 500:
      case 502:
      case 503:
      case 504:
        code = status === 503 ? ERROR_CODES.SERVICE_UNAVAILABLE : ERROR_CODES.INTERNAL_ERROR;
        message = ERROR_MESSAGES[code];
        break;
      default:
        code = ERROR_CODES.INTERNAL_ERROR;
        message = error.message || ERROR_MESSAGES[code];
    }

    return {
      message,
      status,
      code,
      details: error.details || error.response?.data,
    };
  }

  // Default error handling
  return {
    message: error.message || ERROR_MESSAGES[ERROR_CODES.INTERNAL_ERROR],
    status: 500,
    code: ERROR_CODES.INTERNAL_ERROR,
  };
}

/**
 * Gets a user-friendly error message
 */
export function getErrorMessage(error: any): string {
  const parsedError = parseError(error);
  return parsedError.message;
}

/**
 * Determines if an error is retryable
 */
export function isRetryableError(error: any): boolean {
  const parsedError = parseError(error);
  
  // Network errors and timeouts are retryable
  if (parsedError.code === ERROR_CODES.NETWORK_ERROR || 
      parsedError.code === ERROR_CODES.TIMEOUT_ERROR) {
    return true;
  }
  
  // Server errors (5xx) are retryable
  if (parsedError.status && parsedError.status >= 500) {
    return true;
  }
  
  // Rate limiting is retryable after a delay
  if (parsedError.code === ERROR_CODES.RATE_LIMITED) {
    return true;
  }
  
  return false;
}

/**
 * Determines if an error requires authentication
 */
export function requiresAuth(error: any): boolean {
  const parsedError = parseError(error);
  return parsedError.code === ERROR_CODES.UNAUTHORIZED || 
         parsedError.code === ERROR_CODES.TOKEN_EXPIRED;
}

/**
 * Logs an error with appropriate level and context
 */
export function logError(error: any, context?: string): void {
  const parsedError = parseError(error);
  
  const logData = {
    message: parsedError.message,
    status: parsedError.status,
    code: parsedError.code,
    context,
    timestamp: new Date().toISOString(),
    userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : undefined,
    url: typeof window !== 'undefined' ? window.location.href : undefined,
  };

  // In development, log to console
  if (process.env.NODE_ENV === 'development') {
    console.error('Error logged:', logData, error);
  }

  // In production, you might want to send to an error tracking service
  // Example: Sentry, LogRocket, etc.
  // if (process.env.NODE_ENV === 'production') {
  //   errorTrackingService.captureError(error, logData);
  // }
}

/**
 * Creates a retry function with exponential backoff
 */
export function createRetryFunction<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): () => Promise<T> {
  return async (): Promise<T> => {
    let lastError: any;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error;
        
        // Don't retry if it's not a retryable error
        if (!isRetryableError(error)) {
          throw error;
        }
        
        // Don't retry on the last attempt
        if (attempt === maxRetries) {
          break;
        }
        
        // Calculate delay with exponential backoff
        const delay = baseDelay * Math.pow(2, attempt);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    throw lastError;
  };
}
