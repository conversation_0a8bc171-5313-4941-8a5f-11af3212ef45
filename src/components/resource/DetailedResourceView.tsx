import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Entity, ToolDetails, CourseDetails, AgencyDetails, ContentCreatorDetails, CommunityDetails, NewsletterDetails } from '@/types/entity';
import { Review } from '@/types/review';
import ReviewItem from './ReviewItem';
import {
  Star, ExternalLink, Tag as TagIcon, Layers as CategoryIcon, Briefcase as EntityTypeIcon,
  Zap, BookOpen, Users, FileText, Megaphone, Users2, Newspaper, Palette, Share2,
  Bookmark, Globe, Mail, Twitter, Github, Linkedin, MessageCircle, CheckCircle,
  MapPin, Calendar, TrendingUp, Sparkles
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator
} from '@/components/ui/breadcrumb';
import ReviewForm, { ReviewFormData } from './ReviewForm';
import { useAuth } from '@/contexts/AuthContext';
import { useBookmarkContext } from '@/contexts/BookmarkContext';
import { useRouter } from 'next/navigation';
import { useShare } from '@/hooks/useShare';

interface DetailedResourceViewProps {
  entity: Entity;
  reviews: Review[];
  onLoadMoreReviews: () => void;
  hasMoreReviews: boolean;
  isLoadingReviews: boolean;
  reviewsTotalCount?: number;
  onSubmitReview: (data: ReviewFormData) => Promise<void>;
  isSubmittingReview: boolean;
  reviewSubmissionError?: string | null;
  reviewSubmissionSuccess?: string | null;
}

// Helper to format field keys for display
const formatFieldKey = (key: string) => {
  return key
    .replace(/_/g, ' ')
    .replace(/\b\w/g, (char) => char.toUpperCase());
};

// Helper to render a list of strings (e.g., key features, prerequisites)
const renderStringList = (items?: string[], title?: string) => {
  if (!items || items.length === 0) return null;
  return (
    <div className="mt-2">
      {title && <p className="text-sm font-semibold text-gray-600 mb-1">{title}:</p>}
      <ul className="list-disc list-inside pl-1 space-y-0.5">
        {items.map((item, index) => (
          <li key={index} className="text-sm text-gray-600">{item}</li>
        ))}
      </ul>
    </div>
  );
};

const DetailedResourceView: React.FC<DetailedResourceViewProps> = ({
  entity,
  reviews,
  onLoadMoreReviews,
  hasMoreReviews,
  isLoadingReviews,
  reviewsTotalCount,
  onSubmitReview,
  isSubmittingReview,
  reviewSubmissionError,
  reviewSubmissionSuccess
}) => {
  const { session } = useAuth();
  const router = useRouter();
  const { isBookmarked, toggleBookmark } = useBookmarkContext();
  const { shareEntity, getSocialShareUrls } = useShare();
  const [isBookmarkLoading, setIsBookmarkLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  const fallbackImage = '/images/placeholder-logo.png';
  const entityName = entity?.name || 'Unnamed Entity';

  // Data validation helpers
  const isValidUrl = (url: string | null | undefined): boolean => {
    if (!url) return false;
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  const isValidEmail = (email: string | null | undefined): boolean => {
    if (!email) return false;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const sanitizeText = (text: string | null | undefined): string => {
    if (!text) return '';
    return text.trim().replace(/\s+/g, ' ');
  };

  const validateAndFormatYear = (year: number | null | undefined): string | null => {
    if (!year) return null;
    const currentYear = new Date().getFullYear();
    if (year < 1900 || year > currentYear) return null;
    return year.toString();
  };

  const handleBookmark = async () => {
    if (!session) {
      router.push('/login');
      return;
    }

    setIsBookmarkLoading(true);
    try {
      await toggleBookmark(entity.id);
    } catch (error) {
      console.error('Failed to toggle bookmark:', error);
      // You could show a toast notification here
    } finally {
      setIsBookmarkLoading(false);
    }
  };

  const handleShare = async () => {
    try {
      await shareEntity(entity);
    } catch (error) {
      console.error('Failed to share entity:', error);
      // You could show a toast notification here
    }
  };

  // Helper functions for dynamic content
  const hasCompanyInfo = () => {
    return entity.foundedYear ||
           entity.locationSummary ||
           entity.details?.employeeCountRange ||
           entity.details?.fundingStage;
  };

  const hasSocialLinks = () => {
    return entity.socialLinks && Object.values(entity.socialLinks).some(link => link);
  };

  const hasIntegrations = () => {
    return entity.details?.integrations && entity.details.integrations.length > 0;
  };

  const hasSupportOptions = () => {
    return entity.details?.supportChannels?.length > 0 ||
           entity.details?.supportEmail ||
           entity.details?.hasLiveChat ||
           entity.details?.communityUrl ||
           entity.documentationUrl ||
           entity.contactUrl;
  };

  const hasFeatures = () => {
    return (entity.features && entity.features.length > 0) ||
           (entity.details?.keyFeatures && entity.details.keyFeatures.length > 0);
  };

  const hasUseCases = () => {
    return entity.details?.useCases && entity.details.useCases.length > 0;
  };

  const hasPricingInfo = () => {
    return entity.details?.pricingModel ||
           entity.details?.priceRange ||
           entity.details?.pricingDetails ||
           entity.details?.pricingUrl ||
           entity.details?.hasFreeTier;
  };

  // Generate fallback use cases based on entity type and categories
  const getFallbackUseCases = () => {
    const entityType = entity.entityType?.slug;
    const categories = entity.categories?.map(cat => cat.name.toLowerCase()) || [];

    const useCaseMap: Record<string, string[]> = {
      'tool': ['Automation', 'Productivity', 'Data Analysis', 'Content Creation'],
      'course': ['Learning', 'Skill Development', 'Professional Growth', 'Certification'],
      'agency': ['Consulting', 'Implementation', 'Strategy', 'Support'],
      'content-creator': ['Content Creation', 'Education', 'Entertainment', 'Community Building'],
      'community': ['Networking', 'Knowledge Sharing', 'Support', 'Collaboration'],
      'newsletter': ['Information', 'Updates', 'Industry News', 'Learning']
    };

    // Get base use cases from entity type
    let useCases = useCaseMap[entityType] || ['General Use', 'Business Applications'];

    // Enhance based on categories
    if (categories.includes('ai') || categories.includes('artificial intelligence')) {
      useCases = ['AI Integration', 'Machine Learning', 'Automation', 'Data Analysis'];
    }
    if (categories.includes('marketing')) {
      useCases = ['Marketing Campaigns', 'Lead Generation', 'Analytics', 'Content Marketing'];
    }
    if (categories.includes('development') || categories.includes('programming')) {
      useCases = ['Software Development', 'Code Generation', 'Testing', 'Deployment'];
    }

    return useCases.slice(0, 4); // Limit to 4 use cases
  };

  const renderStars = (rating: number) => {
    const stars = [];
    if (typeof rating !== 'number') return <span className="text-gray-500 text-sm">Not rated</span>;
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <Star
          key={i}
          className={`w-5 h-5 ${i <= rating ? 'text-yellow-400' : 'text-gray-300'}`}
          fill={i <= rating ? 'currentColor' : 'none'}
        />,
      );
    }
    return stars;
  };

  const renderEntitySpecificDetails = () => {
    if (!entity.details) return null;

    let detailsContent = null;
    const commonDetailItemClass = "text-sm text-gray-600 dark:text-gray-400";
    const commonDetailLabelClass = "font-semibold text-gray-700 dark:text-gray-300";
    let icon = <Palette className="w-5 h-5 mr-2 text-primary" />; // Default icon
    let detailsTitle = `${entity.entityType?.name || 'Additional'} Details`;

    switch (entity.entityType?.slug) {
      case 'tool':
        const toolDetails = entity.details as ToolDetails;
        icon = <Zap className="w-5 h-5 mr-2 text-primary" />;
        detailsTitle = "Tool Specifications";
        detailsContent = (
          <>
            {toolDetails.technical_level && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Technical Level:</span> {toolDetails.technical_level}</p>}
            {renderStringList(toolDetails.key_features, 'Key Features')}
            {renderStringList(toolDetails.use_cases, 'Use Cases')}
            {renderStringList(toolDetails.integrations, 'Integrations')}
            {toolDetails.pricing_model && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Pricing:</span> {toolDetails.pricing_model}</p>}
            {typeof toolDetails.api_available === 'boolean' && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>API Available:</span> {toolDetails.api_available ? 'Yes' : 'No'}</p>}
            {typeof toolDetails.self_hosted_option === 'boolean' && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Self-Hosted Option:</span> {toolDetails.self_hosted_option ? 'Yes' : 'No'}</p>}
          </>
        );
        break;
      case 'course':
        const courseDetails = entity.details as CourseDetails;
        icon = <BookOpen className="w-5 h-5 mr-2 text-primary" />;
        detailsTitle = "Course Information";
        detailsContent = (
          <>
            {courseDetails.instructor_name && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Instructor:</span> {courseDetails.instructor_name}</p>}
            {courseDetails.duration_text && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Duration:</span> {courseDetails.duration_text}</p>}
            {courseDetails.level && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Level:</span> {courseDetails.level}</p>}
            {renderStringList(courseDetails.prerequisites, 'Prerequisites')}
            {renderStringList(courseDetails.learning_outcomes, 'Learning Outcomes')}
            {courseDetails.language && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Language:</span> {courseDetails.language}</p>}
            {typeof courseDetails.certificate_available === 'boolean' && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Certificate:</span> {courseDetails.certificate_available ? 'Available' : 'Not Available'}</p>}
          </>
        );
        break;
      case 'agency':
        const agencyDetails = entity.details as AgencyDetails;
        icon = <EntityTypeIcon className="w-5 h-5 mr-2 text-primary" />;
        detailsTitle = "Agency Overview";
        detailsContent = (
          <>
            {renderStringList(agencyDetails.services_offered, 'Services Offered')}
            {renderStringList(agencyDetails.specializations, 'Specializations')}
            {agencyDetails.portfolio_url && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Portfolio:</span> <a href={agencyDetails.portfolio_url} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">View Portfolio <ExternalLink className='inline w-3 h-3 ml-0.5' /></a></p>}
            {agencyDetails.team_size && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Team Size:</span> {agencyDetails.team_size}</p>}
            {agencyDetails.region_served && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Region Served:</span> {agencyDetails.region_served}</p>}
            {agencyDetails.contact_email && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Contact:</span> <a href={`mailto:${agencyDetails.contact_email}`} className="text-primary hover:underline">{agencyDetails.contact_email}</a></p>}
          </>
        );
        break;
      case 'content-creator':
        const creatorDetails = entity.details as ContentCreatorDetails;
        icon = <Megaphone className="w-5 h-5 mr-2 text-primary" />;
        detailsTitle = "Creator Profile";
        detailsContent = (
          <>
            {creatorDetails.platform && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Main Platform:</span> {creatorDetails.platform}</p>}
            {creatorDetails.platform_url && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Platform Link:</span> <a href={creatorDetails.platform_url} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">View Platform <ExternalLink className='inline w-3 h-3 ml-0.5' /></a></p>}
            {creatorDetails.subscriber_count && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Subscribers/Followers:</span> {creatorDetails.subscriber_count}</p>}
            {renderStringList(creatorDetails.content_focus, 'Content Focus')}
            {creatorDetails.collaboration_email && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Collaborations:</span> <a href={`mailto:${creatorDetails.collaboration_email}`} className="text-primary hover:underline">{creatorDetails.collaboration_email}</a></p>}
            {creatorDetails.sample_work_links && creatorDetails.sample_work_links.length > 0 && (
                <div className="mt-2">
                    <p className={`${commonDetailLabelClass} mb-1`}>Sample Work:</p>
                    <ul className="list-disc list-inside pl-1 space-y-0.5">
                    {creatorDetails.sample_work_links.map((link, index) => (
                        <li key={index} className={commonDetailItemClass}>
                        <a href={link} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">Sample Link {index + 1} <ExternalLink className='inline w-3 h-3 ml-0.5' /></a>
                        </li>
                    ))}
                    </ul>
                </div>
            )}
          </>
        );
        break;
      case 'community':
        const communityDetails = entity.details as CommunityDetails;
        icon = <Users2 className="w-5 h-5 mr-2 text-primary" />;
        detailsTitle = "Community Hub";
        detailsContent = (
          <>
            {communityDetails.platform_name && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Platform:</span> {communityDetails.platform_name}</p>}
            {communityDetails.platform_url && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Community Link:</span> <a href={communityDetails.platform_url} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">Join Community <ExternalLink className='inline w-3 h-3 ml-0.5' /></a></p>}
            {communityDetails.member_count && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Members:</span> {communityDetails.member_count}</p>}
            {renderStringList(communityDetails.main_topics, 'Main Topics')}
            {communityDetails.moderator_info && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Moderator Info:</span> {communityDetails.moderator_info}</p>}
            {communityDetails.entry_requirements && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Entry Requirements:</span> {communityDetails.entry_requirements}</p>}
          </>
        );
        break;
      case 'newsletter':
        const newsletterDetails = entity.details as NewsletterDetails;
        icon = <Newspaper className="w-5 h-5 mr-2 text-primary" />;
        detailsTitle = "Newsletter Info";
        detailsContent = (
          <>
            {newsletterDetails.author_name && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Author:</span> {newsletterDetails.author_name}</p>}
            {newsletterDetails.publication_schedule && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Schedule:</span> {newsletterDetails.publication_schedule}</p>}
            {newsletterDetails.target_audience && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Target Audience:</span> {newsletterDetails.target_audience}</p>}
            {renderStringList(newsletterDetails.topics_covered, 'Topics Covered')}
            {newsletterDetails.archive_url && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Archive:</span> <a href={newsletterDetails.archive_url} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">View Archive <ExternalLink className='inline w-3 h-3 ml-0.5' /></a></p>}
            {newsletterDetails.subscription_link && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Subscribe:</span> <a href={newsletterDetails.subscription_link} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">Subscribe Here <ExternalLink className='inline w-3 h-3 ml-0.5' /></a></p>}
          </>
        );
        break;
      default:
        if (Object.keys(entity.details).length > 0) {
            detailsTitle = `Additional Details`; // Keep a generic title if type is unknown but details exist
            detailsContent = (
                <>
                {Object.entries(entity.details).map(([key, value]) => (
                    <p key={key} className={commonDetailItemClass}>
                    <span className={commonDetailLabelClass}>{formatFieldKey(key)}:</span> {String(value)}
                    </p>
                ))}
                </>
            );
        }
        break;
    }

    if (!detailsContent) return null;

    return (
      <div className="mt-6 py-6 border-t border-gray-200 dark:border-gray-700">
        <h3 className="text-2xl font-semibold text-gray-800 dark:text-gray-200 mb-4 flex items-center">
          {icon} {detailsTitle} {/* Use dynamic title and icon */}
        </h3>
        <div className="space-y-3">{detailsContent}</div> {/* Added space-y-3 for better spacing within details */}
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Breadcrumb Navigation */}
      <div className="bg-white border-b border-gray-200">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink asChild>
                  <Link href="/">Home</Link>
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink asChild>
                  <Link href="/browse">Browse</Link>
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage>{entityName}</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>
      </div>

      {/* Hero Section */}
      <div className="bg-white border-b border-gray-200">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Logo and Basic Info */}
            <div className="flex flex-col sm:flex-row gap-6 lg:flex-1">
              <div className="relative w-24 h-24 sm:w-32 sm:h-32 flex-shrink-0 bg-gradient-to-br from-indigo-50 to-purple-50 rounded-2xl flex items-center justify-center p-4 shadow-sm">
                <Image
                  src={entity.logoUrl || fallbackImage}
                  alt={`${entityName} logo`}
                  width={128}
                  height={128}
                  className="rounded-xl object-contain"
                />
              </div>

              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h1 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-2">{entityName}</h1>
                    <p className="text-lg text-gray-600 mb-4">{entity.description}</p>
                  </div>
                </div>

                {/* Rating and Metadata */}
                <div className="flex flex-wrap items-center gap-4 mb-6">
                  <div className="flex items-center gap-2">
                    <div className="flex items-center">{renderStars(entity.avgRating)}</div>
                    <span className="text-sm font-medium text-gray-900">{entity.avgRating || 0}</span>
                    <span className="text-sm text-gray-500">({entity.reviewCount} reviews)</span>
                  </div>

                  <Badge variant="secondary" className="bg-indigo-100 text-indigo-700">
                    <EntityTypeIcon className="w-3 h-3 mr-1" />
                    {entity.entityType?.name || 'Tool'}
                  </Badge>

                  <Badge variant="outline">
                    <CategoryIcon className="w-3 h-3 mr-1" />
                    {entity.categories?.[0]?.name || 'AI'}
                  </Badge>

                  {entity.tags && entity.tags.slice(0, 2).map((tag) => (
                    <Badge key={tag.id} variant="outline" className="text-xs">
                      {tag.name}
                    </Badge>
                  ))}
                </div>

                {/* Action Buttons */}
                <div className="flex flex-wrap gap-3">
                  {entity.websiteUrl && (
                    <Button asChild size="lg" className="bg-indigo-600 hover:bg-indigo-700">
                      <Link href={entity.websiteUrl} target="_blank" rel="noopener noreferrer">
                        <Globe className="w-4 h-4 mr-2" />
                        Visit Website
                        <ExternalLink className="w-4 h-4 ml-2" />
                      </Link>
                    </Button>
                  )}

                  <Button
                    variant="outline"
                    size="lg"
                    onClick={handleBookmark}
                    disabled={isBookmarkLoading}
                  >
                    {isBookmarkLoading ? (
                      <div className="w-4 h-4 mr-2 border border-gray-300 border-t-gray-600 rounded-full animate-spin"></div>
                    ) : (
                      <Bookmark className={`w-4 h-4 mr-2 ${isBookmarked(entity.id) ? 'fill-current' : ''}`} />
                    )}
                    {isBookmarked(entity.id) ? 'Saved' : 'Save'}
                  </Button>

                  <Button variant="outline" size="lg" onClick={handleShare}>
                    <Share2 className="w-4 h-4 mr-2" />
                    Share
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Main Content Area */}
          <div className="lg:flex-1">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-4 mb-8">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="features">Features</TabsTrigger>
                <TabsTrigger value="pricing">Pricing</TabsTrigger>
                <TabsTrigger value="reviews">Reviews</TabsTrigger>
              </TabsList>

              {/* Overview Tab */}
              <TabsContent value="overview" className="space-y-8">
                <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">About {entityName}</h2>
                  <div className="prose max-w-none">
                    <p className="text-gray-600 leading-relaxed mb-6">
                      {entity.description || 'No description available.'}
                    </p>
                  </div>

                  {/* Entity Specific Details */}
                  {renderEntitySpecificDetails()}

                  {/* Use Cases - Dynamic with Fallbacks */}
                  <div className="mt-8">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Use Cases</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {(entity.details?.useCases || getFallbackUseCases()).map((useCase, index) => (
                        <div key={index} className="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                          <CheckCircle className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                          <span className="text-gray-700">{useCase}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Tags */}
                  {entity.tags && entity.tags.length > 0 && (
                    <div className="mt-8">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">Tags</h3>
                      <div className="flex flex-wrap gap-2">
                        {entity.tags.map((tag) => (
                          <Badge key={tag.id} variant="secondary" className="bg-gray-100 text-gray-700">
                            <TagIcon className="w-3 h-3 mr-1" />
                            {tag.name}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </TabsContent>

              {/* Features Tab - Dynamic */}
              <TabsContent value="features" className="space-y-6">
                <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Key Features</h2>
                  {hasFeatures() ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* Render entity.features first */}
                      {entity.features?.map((feature) => (
                        <div key={feature.id} className="group flex items-start p-5 border border-gray-200 rounded-xl hover:border-indigo-300 hover:shadow-sm transition-all duration-200">
                          <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-br from-indigo-100 to-indigo-200 rounded-xl flex items-center justify-center mr-4 group-hover:from-indigo-200 group-hover:to-indigo-300 transition-colors">
                            <Sparkles className="w-5 h-5 text-indigo-600" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <h3 className="font-semibold text-gray-900 mb-2">{feature.name}</h3>
                            {feature.description && (
                              <p className="text-sm text-gray-600 leading-relaxed">{feature.description}</p>
                            )}
                          </div>
                        </div>
                      ))}
                      {/* Render entity.details.keyFeatures if available */}
                      {entity.details?.keyFeatures?.map((feature, index) => (
                        <div key={`key-feature-${index}`} className="group flex items-start p-5 border border-gray-200 rounded-xl hover:border-indigo-300 hover:shadow-sm transition-all duration-200">
                          <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-br from-indigo-100 to-indigo-200 rounded-xl flex items-center justify-center mr-4 group-hover:from-indigo-200 group-hover:to-indigo-300 transition-colors">
                            <Sparkles className="w-5 h-5 text-indigo-600" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <h3 className="font-semibold text-gray-900">{feature}</h3>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <Sparkles className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600">No features information available for {entityName}.</p>
                    </div>
                  )}
                </div>
              </TabsContent>

              {/* Pricing Tab - Dynamic */}
              <TabsContent value="pricing" className="space-y-6">
                <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Pricing Information</h2>
                  {hasPricingInfo() ? (
                    <div className="space-y-6">
                      {/* Pricing Model */}
                      {entity.details?.pricingModel && (
                        <div className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl p-6 border border-gray-200">
                          <h3 className="text-lg font-semibold text-gray-900 mb-3">Pricing Model</h3>
                          <Badge variant="secondary" className="text-sm px-3 py-1">
                            {entity.details.pricingModel.replace('_', ' ')}
                          </Badge>
                        </div>
                      )}

                      {/* Price Range */}
                      {entity.details?.priceRange && (
                        <div className="bg-gray-50 rounded-lg p-4">
                          <h3 className="text-lg font-semibold text-gray-900 mb-2">Price Range</h3>
                          <Badge variant="outline" className="text-sm">
                            {entity.details.priceRange}
                          </Badge>
                        </div>
                      )}

                      {/* Free Tier */}
                      {entity.details?.hasFreeTier && (
                        <div className="bg-green-50 rounded-lg p-4 border border-green-200">
                          <div className="flex items-center">
                            <CheckCircle className="w-5 h-5 text-green-500 mr-3" />
                            <span className="text-green-800 font-medium">Free tier available</span>
                          </div>
                        </div>
                      )}

                      {/* Pricing Details */}
                      {entity.details?.pricingDetails && (
                        <div className="bg-gray-50 rounded-lg p-4">
                          <h3 className="text-lg font-semibold text-gray-900 mb-2">Details</h3>
                          <p className="text-gray-600">{entity.details.pricingDetails}</p>
                        </div>
                      )}

                      {/* Pricing URL */}
                      {entity.details?.pricingUrl && (
                        <div className="text-center">
                          <Button asChild className="bg-indigo-600 hover:bg-indigo-700">
                            <Link href={entity.details.pricingUrl} target="_blank" rel="noopener noreferrer">
                              View Detailed Pricing
                              <ExternalLink className="w-4 h-4 ml-2" />
                            </Link>
                          </Button>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <span className="text-2xl">💰</span>
                      </div>
                      <p className="text-gray-600">No pricing information available for {entityName}.</p>
                      {entity.websiteUrl && (
                        <Button asChild variant="outline" className="mt-4">
                          <Link href={entity.websiteUrl} target="_blank" rel="noopener noreferrer">
                            Visit Website for Pricing
                            <ExternalLink className="w-4 h-4 ml-2" />
                          </Link>
                        </Button>
                      )}
                    </div>
                  )}
                </div>
              </TabsContent>

              {/* Reviews Tab */}
              <TabsContent value="reviews" className="space-y-6">
                <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-2xl font-bold text-gray-900">
                      User Reviews ({reviews?.length || 0}
                      {reviewsTotalCount && reviewsTotalCount > (reviews?.length || 0) ? ` of ${reviewsTotalCount}` : ''})
                    </h2>
                  </div>

                  {reviews && reviews.length > 0 ? (
                    <div className="space-y-6">
                      {reviews.map((review) => (
                        <ReviewItem key={review.id} review={review} />
                      ))}
                    </div>
                  ) : (
                    !isLoadingReviews && (
                      <div className="text-center py-12">
                        <MessageCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-600">No reviews yet for {entityName}. Be the first to share your thoughts!</p>
                      </div>
                    )
                  )}

                  {isLoadingReviews && (
                    <div className="text-center py-8">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
                      <p className="text-gray-500 mt-2">Loading reviews...</p>
                    </div>
                  )}

                  {hasMoreReviews && !isLoadingReviews && (
                    <div className="mt-8 text-center">
                      <Button onClick={onLoadMoreReviews} variant="outline" size="lg">
                        Load More Reviews
                      </Button>
                    </div>
                  )}

                  {/* Write a Review Section */}
                  {session ? (
                    <div className="mt-8 pt-8 border-t border-gray-200">
                      <h3 className="text-lg font-semibold text-gray-900 mb-6">Write a Review for {entityName}</h3>
                      <ReviewForm
                        entityId={entity.id}
                        onSubmitReview={onSubmitReview}
                        isSubmitting={isSubmittingReview}
                        formError={reviewSubmissionError}
                        formSuccess={reviewSubmissionSuccess}
                      />
                    </div>
                  ) : (
                    <div className="mt-8 pt-8 border-t border-gray-200 text-center">
                      <p className="text-gray-600">
                        Please <Link href="/login" className="text-indigo-600 hover:text-indigo-700 font-medium">log in</Link> to write a review.
                      </p>
                    </div>
                  )}
                </div>
              </TabsContent>
            </Tabs>
          </div>

          {/* Sidebar */}
          <div className="lg:w-80 space-y-8">
            {/* Quick Actions - Dynamic */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
              <div className="space-y-3">
                {entity.websiteUrl && (
                  <Button asChild className="w-full bg-indigo-600 hover:bg-indigo-700">
                    <Link href={entity.websiteUrl} target="_blank" rel="noopener noreferrer">
                      <Globe className="w-4 h-4 mr-2" />
                      Visit Website
                    </Link>
                  </Button>
                )}
                {isValidEmail(entity.details?.supportEmail) && (
                  <Button asChild variant="outline" className="w-full">
                    <Link href={`mailto:${entity.details.supportEmail}`}>
                      <Mail className="w-4 h-4 mr-2" />
                      Contact Support
                    </Link>
                  </Button>
                )}
                {entity.contactUrl && (
                  <Button asChild variant="outline" className="w-full">
                    <Link href={entity.contactUrl} target="_blank" rel="noopener noreferrer">
                      <Mail className="w-4 h-4 mr-2" />
                      Contact
                    </Link>
                  </Button>
                )}
                {entity.documentationUrl && (
                  <Button asChild variant="outline" className="w-full">
                    <Link href={entity.documentationUrl} target="_blank" rel="noopener noreferrer">
                      <FileText className="w-4 h-4 mr-2" />
                      Documentation
                    </Link>
                  </Button>
                )}
              </div>
            </div>

            {/* Company Info - Dynamic */}
            {hasCompanyInfo() && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Company Info</h3>
                <div className="space-y-3">
                  {validateAndFormatYear(entity.foundedYear) && (
                    <div className="flex items-center text-sm">
                      <Calendar className="w-4 h-4 text-gray-400 mr-3" />
                      <span className="text-gray-600">Founded {validateAndFormatYear(entity.foundedYear)}</span>
                    </div>
                  )}
                  {entity.details?.employeeCountRange && (
                    <div className="flex items-center text-sm">
                      <Users className="w-4 h-4 text-gray-400 mr-3" />
                      <span className="text-gray-600">{entity.details.employeeCountRange} employees</span>
                    </div>
                  )}
                  {entity.details?.fundingStage && (
                    <div className="flex items-center text-sm">
                      <TrendingUp className="w-4 h-4 text-gray-400 mr-3" />
                      <span className="text-gray-600">{entity.details.fundingStage} funding</span>
                    </div>
                  )}
                  {entity.locationSummary && (
                    <div className="flex items-center text-sm">
                      <MapPin className="w-4 h-4 text-gray-400 mr-3" />
                      <span className="text-gray-600">{entity.locationSummary}</span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Social Links - Dynamic */}
            {hasSocialLinks() && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Connect</h3>
                <div className="flex flex-wrap gap-3">
                  {isValidUrl(entity.socialLinks?.twitter) && (
                    <Button variant="outline" size="sm" asChild>
                      <Link href={entity.socialLinks.twitter} target="_blank" rel="noopener noreferrer">
                        <Twitter className="w-4 h-4" />
                      </Link>
                    </Button>
                  )}
                  {isValidUrl(entity.socialLinks?.github) && (
                    <Button variant="outline" size="sm" asChild>
                      <Link href={entity.socialLinks.github} target="_blank" rel="noopener noreferrer">
                        <Github className="w-4 h-4" />
                      </Link>
                    </Button>
                  )}
                  {isValidUrl(entity.socialLinks?.linkedin) && (
                    <Button variant="outline" size="sm" asChild>
                      <Link href={entity.socialLinks.linkedin} target="_blank" rel="noopener noreferrer">
                        <Linkedin className="w-4 h-4" />
                      </Link>
                    </Button>
                  )}
                  {entity.socialLinks?.youtube && (
                    <Button variant="outline" size="sm" asChild>
                      <Link href={entity.socialLinks.youtube} target="_blank" rel="noopener noreferrer">
                        <span className="w-4 h-4 text-red-600">▶</span>
                      </Link>
                    </Button>
                  )}
                  {entity.socialLinks?.facebook && (
                    <Button variant="outline" size="sm" asChild>
                      <Link href={entity.socialLinks.facebook} target="_blank" rel="noopener noreferrer">
                        <span className="w-4 h-4 text-blue-600">f</span>
                      </Link>
                    </Button>
                  )}
                  {entity.socialLinks?.instagram && (
                    <Button variant="outline" size="sm" asChild>
                      <Link href={entity.socialLinks.instagram} target="_blank" rel="noopener noreferrer">
                        <span className="w-4 h-4 text-pink-600">📷</span>
                      </Link>
                    </Button>
                  )}
                  {entity.socialLinks?.discord && (
                    <Button variant="outline" size="sm" asChild>
                      <Link href={entity.socialLinks.discord} target="_blank" rel="noopener noreferrer">
                        <MessageCircle className="w-4 h-4" />
                      </Link>
                    </Button>
                  )}
                </div>
              </div>
            )}

            {/* Integrations - Dynamic */}
            {hasIntegrations() && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Integrations</h3>
                <div className="flex flex-wrap gap-2">
                  {entity.details?.integrations?.map((integration, index) => (
                    <Badge key={index} variant="secondary" className="bg-gray-100 text-gray-700">
                      {integration}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Support Options - Dynamic */}
            {hasSupportOptions() && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Support</h3>
                <div className="space-y-2">
                  {entity.details?.supportChannels?.map((channel, index) => (
                    <div key={index} className="flex items-center text-sm">
                      <CheckCircle className="w-4 h-4 text-green-500 mr-3" />
                      <span className="text-gray-600">{channel}</span>
                    </div>
                  ))}
                  {entity.details?.supportEmail && (
                    <div className="flex items-center text-sm">
                      <CheckCircle className="w-4 h-4 text-green-500 mr-3" />
                      <span className="text-gray-600">Email Support</span>
                    </div>
                  )}
                  {entity.details?.hasLiveChat && (
                    <div className="flex items-center text-sm">
                      <CheckCircle className="w-4 h-4 text-green-500 mr-3" />
                      <span className="text-gray-600">Live Chat</span>
                    </div>
                  )}
                  {entity.documentationUrl && (
                    <div className="flex items-center text-sm">
                      <CheckCircle className="w-4 h-4 text-green-500 mr-3" />
                      <span className="text-gray-600">Documentation</span>
                    </div>
                  )}
                  {entity.details?.communityUrl && (
                    <div className="flex items-center text-sm">
                      <CheckCircle className="w-4 h-4 text-green-500 mr-3" />
                      <span className="text-gray-600">Community Forum</span>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DetailedResourceView; 