'use client';

import React, { useState } from 'react';
import { Entity } from '@/types/entity';
import DetailedResourceView from './DetailedResourceView';
import { Button } from '@/components/ui/button';

// Mock entity data to demonstrate the improved UI
const mockEntity: Entity = {
  id: 'mock-entity-1',
  name: 'AI Code Assistant Pro',
  slug: 'ai-code-assistant-pro',
  description: 'An advanced AI-powered code assistant that helps developers write better code faster with intelligent suggestions, automated refactoring, and comprehensive code analysis.',
  shortDescription: 'AI-powered code assistant for developers',
  websiteUrl: 'https://example.com',
  logoUrl: '/images/placeholder-logo.png',
  documentationUrl: 'https://example.com/docs',
  contactUrl: 'https://example.com/contact',
  privacyPolicyUrl: 'https://example.com/privacy',
  foundedYear: 2022,
  entityType: {
    id: 'tool-type',
    name: 'Tool',
    slug: 'tool',
    description: 'Software tools and applications',
    iconUrl: null,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  categories: [
    {
      id: 'ai-category',
      name: 'AI & Machine Learning',
      slug: 'ai-ml',
      description: 'AI and ML tools',
      iconUrl: null,
      parentCategoryId: null,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    }
  ],
  tags: [
    { id: 'tag1', name: 'Code Generation', slug: 'code-generation' },
    { id: 'tag2', name: 'Developer Tools', slug: 'developer-tools' },
    { id: 'tag3', name: 'AI Assistant', slug: 'ai-assistant' }
  ],
  features: [
    {
      id: 'feature1',
      name: 'Intelligent Code Completion',
      slug: 'intelligent-completion',
      description: 'AI-powered code suggestions and completions',
      iconUrl: null,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    },
    {
      id: 'feature2',
      name: 'Automated Refactoring',
      slug: 'auto-refactoring',
      description: 'Automatically improve code structure and quality',
      iconUrl: null,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    }
  ],
  avgRating: 4.7,
  reviewCount: 156,
  saveCount: 1240,
  status: 'ACTIVE',
  socialLinks: {
    twitter: 'https://twitter.com/example',
    github: 'https://github.com/example',
    linkedin: 'https://linkedin.com/company/example'
  },
  submitter: {
    id: 'user1',
    user_metadata: {
      display_name: 'John Developer'
    }
  },
  legacyId: null,
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-06-20T00:00:00Z',
  metaTitle: 'AI Code Assistant Pro - Advanced Developer Tool',
  metaDescription: 'Boost your coding productivity with AI Code Assistant Pro',
  scrapedReviewSentimentLabel: 'POSITIVE',
  scrapedReviewSentimentScore: 0.85,
  scrapedReviewCount: 89,
  employeeCountRange: '11-50',
  fundingStage: 'Series A',
  locationSummary: 'San Francisco, CA',
  refLink: null,
  affiliateStatus: null,
  hasFreeTier: true,
  details: {
    entityId: 'mock-entity-1',
    technical_level: 'INTERMEDIATE',
    learning_curve: 'LOW',
    customization_level: 'HIGH',
    api_available: true,
    self_hosted_option: true,
    mobile_support: false,
    key_features: [
      'AI-powered code completion',
      'Real-time error detection',
      'Automated testing suggestions',
      'Code quality analysis',
      'Multi-language support'
    ],
    use_cases: [
      'Web development',
      'Mobile app development',
      'API development',
      'Code review automation',
      'Legacy code modernization'
    ],
    integrations: [
      'VS Code',
      'IntelliJ IDEA',
      'GitHub',
      'GitLab',
      'Slack',
      'Jira'
    ],
    programming_languages: [
      'JavaScript',
      'TypeScript',
      'Python',
      'Java',
      'C#',
      'Go',
      'Rust'
    ],
    supported_os: [
      'Windows',
      'macOS',
      'Linux'
    ],
    pricingModel: 'FREEMIUM',
    hasFreeTier: true,
    supportChannels: [
      'Email Support',
      'Live Chat',
      'Community Forum',
      'Documentation'
    ]
  }
};

const MockEntityDemo: React.FC = () => {
  const [showDemo, setShowDemo] = useState(false);

  if (!showDemo) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center max-w-md mx-auto px-4">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">UI Demo</h1>
          <p className="text-gray-600 mb-6">
            The backend API is currently experiencing database issues. 
            Click below to see a demo of the improved entity details UI with mock data.
          </p>
          <Button onClick={() => setShowDemo(true)} className="bg-indigo-600 hover:bg-indigo-700">
            View UI Demo
          </Button>
        </div>
      </div>
    );
  }

  return (
    <DetailedResourceView
      entity={mockEntity}
      reviews={[]}
      onLoadMoreReviews={() => {}}
      hasMoreReviews={false}
      isLoadingReviews={false}
      reviewsTotalCount={0}
      onSubmitReview={async () => {}}
      isSubmittingReview={false}
      reviewSubmissionError={null}
      reviewSubmissionSuccess={null}
    />
  );
};

export default MockEntityDemo;
