import React from 'react';
import { Metadata } from 'next';
import { getEntityBySlug } from '@/services/api';
import EntityPageClient from './EntityPageClient';

// Generate metadata for SEO
export async function generateMetadata({ params }: { params: { slug: string } }): Promise<Metadata> {
  try {
    const entity = await getEntityBySlug(params.slug);

    const title = entity.metaTitle || `${entity.name} | AI Navigator`;
    const description = entity.metaDescription || entity.shortDescription || entity.description || `Discover ${entity.name} on AI Navigator`;
    const url = `${process.env.NEXT_PUBLIC_SITE_URL || 'https://ai-navigator.com'}/entities/${entity.slug}`;
    const imageUrl = entity.logoUrl || `${process.env.NEXT_PUBLIC_SITE_URL || 'https://ai-navigator.com'}/images/og-default.png`;

    return {
      title,
      description,
      keywords: [
        entity.name,
        entity.entityType.name,
        ...entity.categories.map(cat => cat.name),
        ...entity.tags.map(tag => tag.name),
        ...entity.features.map(feature => feature.name),
        'AI', 'artificial intelligence', 'tools', 'resources'
      ].join(', '),
      authors: [{ name: 'AI Navigator' }],
      creator: 'AI Navigator',
      publisher: 'AI Navigator',
      robots: {
        index: entity.status === 'ACTIVE',
        follow: true,
        googleBot: {
          index: entity.status === 'ACTIVE',
          follow: true,
        },
      },
      openGraph: {
        type: 'website',
        url,
        title,
        description,
        siteName: 'AI Navigator',
        images: [
          {
            url: imageUrl,
            width: 1200,
            height: 630,
            alt: `${entity.name} logo`,
          },
        ],
      },
      twitter: {
        card: 'summary_large_image',
        title,
        description,
        images: [imageUrl],
        creator: '@ai_navigator',
      },
      alternates: {
        canonical: url,
      },
    };
  } catch (error) {
    console.error('Error generating metadata:', error);
    return {
      title: 'Entity Not Found | AI Navigator',
      description: 'The requested entity could not be found.',
    };
  }
}

export default function EntitySlugPage({ params }: { params: { slug: string } }) {
  return <EntityPageClient slug={params.slug} />;
}
